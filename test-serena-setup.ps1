# Test Serena MCP Server Configuration
$env:Path = "C:\Users\<USER>\.local\bin;$env:Path"

Write-Host "Testing Serena MCP Server Configuration..." -ForegroundColor Green
Write-Host ""

# Test 1: Check if uvx is available
Write-Host "1. Checking uvx availability..."
try {
    $uvxVersion = uvx --version 2>$null
    Write-Host "   ✓ uvx is available: $uvxVersion" -ForegroundColor Green
} catch {
    Write-Host "   ✗ uvx is not available in PATH" -ForegroundColor Red
    exit 1
}

# Test 2: Check if <PERSON> can be invoked
Write-Host "2. Testing Serena MCP server invocation..."
try {
    $serenaHelp = uvx --from git+https://github.com/oraios/serena serena-mcp-server --help 2>&1
    if ($LASTEXITCODE -eq 0 -or $serenaHelp -match "Usage: serena-mcp-server") {
        Write-Host "   ✓ Serena MCP server can be invoked" -ForegroundColor Green
    } else {
        Write-Host "   ✗ Serena MCP server invocation failed" -ForegroundColor Red
        Write-Host "   Error: $serenaHelp" -ForegroundColor Red
    }
} catch {
    Write-Host "   ✗ Error invoking Serena MCP server: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 3: Check VS Code settings
Write-Host "3. Checking VS Code settings..."
$settingsPath = "$env:APPDATA\Code\User\settings.json"
if (Test-Path $settingsPath) {
    Write-Host "   ✓ VS Code settings file exists" -ForegroundColor Green
    try {
        $settings = Get-Content $settingsPath -Raw | ConvertFrom-Json
        if ($settings.'copilot-mcp.mcpServers'.serena) {
            Write-Host "   ✓ Serena MCP server configured in VS Code settings" -ForegroundColor Green
        } else {
            Write-Host "   ⚠ Serena MCP server configuration not found in settings" -ForegroundColor Yellow
        }
    } catch {
        Write-Host "   ⚠ Could not parse VS Code settings" -ForegroundColor Yellow
    }
} else {
    Write-Host "   ✗ VS Code settings file not found" -ForegroundColor Red
}

Write-Host ""
Write-Host "Test completed!" -ForegroundColor Green
Write-Host ""
Write-Host "Next steps:" -ForegroundColor Cyan
Write-Host "1. Restart VS Code completely"
Write-Host "2. Open GitHub Copilot Chat (Ctrl+Shift+I)"
Write-Host "3. Try: 'Can you use Serena to activate the current project?'"
Write-Host "4. Check if Serena tools are available in MCP-compatible extensions"
