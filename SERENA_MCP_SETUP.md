# Serena MCP Server Setup for VS Code

## Overview
This setup configures the Serena MCP (Model Context Protocol) server to work with GitHub Copilot, Augment, and other AI coding assistants in VS Code.

## What's Installed
- **uv** - Python package manager (installed to: `C:\Users\<USER>\.local\bin\`)
- **Serena MCP Server** - AI coding agent toolkit with semantic code analysis
- **Copilot MCP Extension** - Bridge between GitHub Copilot and MCP servers

## Configuration Details
The following extensions can now use <PERSON>'s tools:
- **GitHub Copilot** (with Copilot MCP extension)
- **Augment** 
- **Cline**
- **Roo Code**
- **Kilo Code AI Agent**

## MCP Server Configuration
```json
{
  "copilot-mcp.mcpServers": {
    "serena": {
      "command": "C:\\Users\\<USER>\\.local\\bin\\uvx.exe",
      "args": [
        "--from", 
        "git+https://github.com/oraios/serena", 
        "serena-mcp-server",
        "--context",
        "ide-assistant",
        "--project",
        "c:\\Users\\<USER>\\Dropbox\\AL\\Ipek-Pamuk-Customizations"
      ]
    }
  }
}
```

## Serena's Capabilities
- **Semantic Code Analysis** - Understanding code structure using language servers
- **Symbol-level Operations** - Find definitions, references, implementations
- **Code Generation** - Create/edit files with intelligent context
- **Shell Execution** - Run commands and tests
- **Project Management** - Activate and manage projects
- **Memory System** - Remember project context across sessions

## How to Use

### With GitHub Copilot
1. Open GitHub Copilot Chat (Ctrl+Shift+I)
2. Ask Copilot to use Serena's tools, for example:
   - "Use Serena to find all references to this function"
   - "Activate the current project with Serena"
   - "Use Serena to analyze the code structure"

### With Cline/Roo Code
1. Open the respective extension
2. Serena's tools should be automatically available
3. The AI agent can now use semantic code analysis

## Testing the Setup
To verify everything is working:

1. **Restart VS Code** after configuration
2. Open GitHub Copilot Chat
3. Try this command: "Can you use Serena to activate the current project?"
4. Check if Serena's tools are available in the MCP client interfaces

## Available Serena Tools
- `activate_project` - Activate a project for analysis
- `find_symbol` - Search for symbols in code
- `get_symbols_overview` - Get overview of file/directory symbols
- `read_file` - Read project files
- `create_text_file` - Create/edit files
- `execute_shell_command` - Run shell commands
- `find_referencing_symbols` - Find symbol references
- And many more...

## Troubleshooting

### If MCP server doesn't start:
1. Ensure uv is in PATH: `C:\Users\<USER>\.local\bin\`
2. Test Serena manually: `uvx --from git+https://github.com/oraios/serena serena-mcp-server --help`
3. Check VS Code Developer Console for errors

### If tools aren't available:
1. Verify the Copilot MCP extension is installed and enabled
2. Check that GitHub Copilot is properly authenticated
3. Restart VS Code completely

### Path Issues:
- All paths use forward slashes or properly escaped backslashes
- Absolute paths are used to avoid relative path issues

## Context Configuration
- **Context**: `ide-assistant` - Optimized for IDE integration
- **Project**: Pre-configured for your AL project at `c:\Users\<USER>\Dropbox\AL\Ipek-Pamuk-Customizations`

## Next Steps
1. Restart VS Code
2. Test with GitHub Copilot Chat
3. Try asking Copilot to "activate the current project with Serena"
4. Explore Serena's semantic code analysis capabilities
