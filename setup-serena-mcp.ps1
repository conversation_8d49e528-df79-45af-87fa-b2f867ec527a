# Setup Serena MCP Server for VS Code
# This script configures Serena MCP server for GitHub Copilot and other MCP-compatible extensions

$userSettingsPath = "$env:APPDATA\Code\User\settings.json"

# Backup existing settings if they exist
if (Test-Path $userSettingsPath) {
    $backupPath = "$userSettingsPath.backup.$(Get-Date -Format 'yyyyMMdd-HHmmss')"
    Copy-Item $userSettingsPath $backupPath
    Write-Host "Backed up existing settings to: $backupPath"
}

# Read existing settings or create empty object
$settings = @{}
if (Test-Path $userSettingsPath) {
    try {
        $content = Get-Content $userSettingsPath -Raw
        $settings = $content | ConvertFrom-Json -AsHashtable
    } catch {
        Write-Host "Warning: Could not parse existing settings, creating new configuration"
        $settings = @{}
    }
}

# Add MCP server configuration for Serena
$mcpServers = @{
    "serena" = @{
        "command" = "C:\Users\<USER>\.local\bin\uvx.exe"
        "args" = @(
            "--from", 
            "git+https://github.com/oraios/serena", 
            "serena-mcp-server",
            "--context",
            "ide-assistant",
            "--project",
            "c:\Users\<USER>\Dropbox\AL\Ipek-Pamuk-Customizations"
        )
    }
}

# Configure MCP settings for various extensions
$settings["copilot-mcp.mcpServers"] = $mcpServers
$settings["mcp.servers"] = $mcpServers

# Additional Copilot settings to enhance MCP integration
$settings["github.copilot.enable"] = @{
    "*" = $true
    "plaintext" = $true
    "markdown" = $true
    "scminput" = $false
}

# Convert back to JSON and save
$jsonSettings = $settings | ConvertTo-Json -Depth 10
$jsonSettings | Set-Content $userSettingsPath -Encoding UTF8

Write-Host "Successfully configured Serena MCP server for VS Code!"
Write-Host "Configuration saved to: $userSettingsPath"
Write-Host ""
Write-Host "Next steps:"
Write-Host "1. Restart VS Code"
Write-Host "2. The Serena MCP server should now be available to GitHub Copilot and other MCP clients"
Write-Host "3. You can test it by asking Copilot to use Serena's tools"
Write-Host ""
Write-Host "Available MCP extensions:"
Write-Host "- GitHub Copilot (with Copilot MCP extension)"
Write-Host "- Cline"
Write-Host "- Roo Code"
Write-Host "- Kilo Code AI Agent"
